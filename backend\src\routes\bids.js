const express = require('express');
const db = require('../config/database');
const { authenticateToken, requireUserType } = require('../middleware/auth');

const router = express.Router();

// 出价
router.post('/', authenticateToken, requireUserType(['buyer']), async (req, res) => {
  const connection = await db.getConnection();
  
  try {
    await connection.beginTransaction();
    
    const { auction_id, amount } = req.body;
    const user_id = req.user.id;

    // 验证拍卖是否存在且正在进行
    const [auctions] = await connection.execute(`
      SELECT 
        a.*,
        v.seller_id
      FROM auctions a
      JOIN vehicles v ON a.vehicle_id = v.id
      WHERE a.id = ? AND a.status = 'active' AND a.end_time > NOW()
    `, [auction_id]);

    if (auctions.length === 0) {
      await connection.rollback();
      return res.status(400).json({
        success: false,
        message: '拍卖不存在或已结束'
      });
    }

    const auction = auctions[0];

    // 验证不能对自己的车辆出价
    if (auction.seller_id === user_id) {
      await connection.rollback();
      return res.status(400).json({
        success: false,
        message: '不能对自己的车辆出价'
      });
    }

    // 验证出价金额
    const minBidAmount = Math.max(
      auction.starting_price,
      auction.current_price + auction.bid_increment
    );

    if (amount < minBidAmount) {
      await connection.rollback();
      return res.status(400).json({
        success: false,
        message: `出价不能低于 ${minBidAmount} 元`
      });
    }

    // 验证用户是否已缴纳保证金
    const [deposits] = await connection.execute(
      'SELECT * FROM deposits WHERE user_id = ? AND auction_id = ? AND status = "paid"',
      [user_id, auction_id]
    );

    if (deposits.length === 0) {
      await connection.rollback();
      return res.status(400).json({
        success: false,
        message: '请先缴纳保证金'
      });
    }

    // 记录出价
    await connection.execute(
      'INSERT INTO bids (auction_id, user_id, amount) VALUES (?, ?, ?)',
      [auction_id, user_id, amount]
    );

    // 更新拍卖当前价格和出价次数
    await connection.execute(`
      UPDATE auctions 
      SET current_price = ?, total_bids = total_bids + 1
      WHERE id = ?
    `, [amount, auction_id]);

    // 检查是否需要延时（最后5分钟内出价延时5分钟）
    const timeLeft = new Date(auction.end_time) - new Date();
    if (timeLeft <= 5 * 60 * 1000) { // 5分钟
      const newEndTime = new Date(Date.now() + 5 * 60 * 1000);
      await connection.execute(
        'UPDATE auctions SET end_time = ? WHERE id = ?',
        [newEndTime, auction_id]
      );
    }

    await connection.commit();

    // 通过WebSocket广播出价信息
    const io = req.app.get('io');
    if (io) {
      io.to(`auction_${auction_id}`).emit('new_bid', {
        auction_id,
        amount,
        user_nickname: req.user.nickname,
        bid_time: new Date(),
        total_bids: auction.total_bids + 1
      });
    }

    res.json({
      success: true,
      message: '出价成功'
    });

  } catch (error) {
    await connection.rollback();
    console.error('出价错误:', error);
    res.status(500).json({
      success: false,
      message: '出价失败'
    });
  } finally {
    connection.release();
  }
});

// 获取我的出价记录
router.get('/my', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;
    const offset = (page - 1) * limit;

    let whereCondition = 'WHERE b.user_id = ?';
    let params = [req.user.id];

    if (status) {
      whereCondition += ' AND a.status = ?';
      params.push(status);
    }

    const [bids] = await db.execute(`
      SELECT 
        b.id,
        b.amount,
        b.bid_time,
        a.id as auction_id,
        a.title,
        a.current_price,
        a.status as auction_status,
        a.end_time,
        a.winner_id,
        v.brand,
        v.model,
        v.images,
        CASE 
          WHEN a.winner_id = b.user_id THEN 'won'
          WHEN a.status = 'ended' AND a.winner_id != b.user_id THEN 'lost'
          WHEN a.status = 'active' AND b.amount = a.current_price THEN 'leading'
          WHEN a.status = 'active' AND b.amount < a.current_price THEN 'outbid'
          ELSE 'active'
        END as bid_status
      FROM bids b
      JOIN auctions a ON b.auction_id = a.id
      JOIN vehicles v ON a.vehicle_id = v.id
      ${whereCondition}
      ORDER BY b.bid_time DESC
      LIMIT ? OFFSET ?
    `, [...params, parseInt(limit), offset]);

    res.json({
      success: true,
      data: bids.map(bid => ({
        ...bid,
        images: JSON.parse(bid.images || '[]')
      }))
    });

  } catch (error) {
    console.error('获取出价记录错误:', error);
    res.status(500).json({
      success: false,
      message: '获取出价记录失败'
    });
  }
});

// 获取拍卖的出价记录
router.get('/auction/:auction_id', async (req, res) => {
  try {
    const { auction_id } = req.params;
    const { page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    const [bids] = await db.execute(`
      SELECT 
        b.amount,
        b.bid_time,
        u.nickname,
        u.avatar
      FROM bids b
      JOIN users u ON b.user_id = u.id
      WHERE b.auction_id = ?
      ORDER BY b.bid_time DESC
      LIMIT ? OFFSET ?
    `, [auction_id, parseInt(limit), offset]);

    res.json({
      success: true,
      data: bids
    });

  } catch (error) {
    console.error('获取拍卖出价记录错误:', error);
    res.status(500).json({
      success: false,
      message: '获取出价记录失败'
    });
  }
});

// 缴纳保证金
router.post('/deposit', authenticateToken, requireUserType(['buyer']), async (req, res) => {
  try {
    const { auction_id, payment_method, transaction_id } = req.body;
    const user_id = req.user.id;

    // 验证拍卖是否存在
    const [auctions] = await db.execute(
      'SELECT * FROM auctions WHERE id = ? AND status IN ("upcoming", "active")',
      [auction_id]
    );

    if (auctions.length === 0) {
      return res.status(400).json({
        success: false,
        message: '拍卖不存在或已结束'
      });
    }

    const auction = auctions[0];

    // 检查是否已缴纳保证金
    const [existingDeposits] = await db.execute(
      'SELECT * FROM deposits WHERE user_id = ? AND auction_id = ?',
      [user_id, auction_id]
    );

    if (existingDeposits.length > 0) {
      return res.status(400).json({
        success: false,
        message: '已缴纳保证金'
      });
    }

    // 记录保证金
    await db.execute(`
      INSERT INTO deposits (
        user_id, auction_id, amount, payment_method, transaction_id
      ) VALUES (?, ?, ?, ?, ?)
    `, [user_id, auction_id, auction.deposit_amount, payment_method, transaction_id]);

    res.json({
      success: true,
      message: '保证金缴纳成功'
    });

  } catch (error) {
    console.error('缴纳保证金错误:', error);
    res.status(500).json({
      success: false,
      message: '缴纳保证金失败'
    });
  }
});

// 获取我的保证金记录
router.get('/deposits/my', authenticateToken, async (req, res) => {
  try {
    const [deposits] = await db.execute(`
      SELECT 
        d.*,
        a.title,
        v.brand,
        v.model
      FROM deposits d
      JOIN auctions a ON d.auction_id = a.id
      JOIN vehicles v ON a.vehicle_id = v.id
      WHERE d.user_id = ?
      ORDER BY d.paid_at DESC
    `, [req.user.id]);

    res.json({
      success: true,
      data: deposits
    });

  } catch (error) {
    console.error('获取保证金记录错误:', error);
    res.status(500).json({
      success: false,
      message: '获取保证金记录失败'
    });
  }
});

module.exports = router;
