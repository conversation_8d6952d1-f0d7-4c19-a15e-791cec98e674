const express = require('express');
const jwt = require('jsonwebtoken');
const axios = require('axios');
const db = require('../config/database');

const router = express.Router();

// 微信小程序登录
router.post('/wechat-login', async (req, res) => {
  try {
    const { code, userInfo } = req.body;

    if (!code) {
      return res.status(400).json({
        success: false,
        message: '缺少授权码'
      });
    }

    // 调用微信API获取openid
    const wechatResponse = await axios.get('https://api.weixin.qq.com/sns/jscode2session', {
      params: {
        appid: process.env.WECHAT_APPID,
        secret: process.env.WECHAT_SECRET,
        js_code: code,
        grant_type: 'authorization_code'
      }
    });

    const { openid, session_key } = wechatResponse.data;

    if (!openid) {
      return res.status(400).json({
        success: false,
        message: '微信登录失败',
        error: wechatResponse.data
      });
    }

    // 查找或创建用户
    let [users] = await db.execute(
      'SELECT * FROM users WHERE openid = ?',
      [openid]
    );

    let user;
    if (users.length === 0) {
      // 创建新用户
      const [result] = await db.execute(
        'INSERT INTO users (openid, nickname, avatar) VALUES (?, ?, ?)',
        [openid, userInfo?.nickName || '', userInfo?.avatarUrl || '']
      );
      
      user = {
        id: result.insertId,
        openid,
        nickname: userInfo?.nickName || '',
        avatar: userInfo?.avatarUrl || '',
        user_type: 'buyer',
        level: 'normal',
        status: 'active'
      };
    } else {
      user = users[0];
      
      // 更新用户信息
      if (userInfo) {
        await db.execute(
          'UPDATE users SET nickname = ?, avatar = ? WHERE id = ?',
          [userInfo.nickName || user.nickname, userInfo.avatarUrl || user.avatar, user.id]
        );
        user.nickname = userInfo.nickName || user.nickname;
        user.avatar = userInfo.avatarUrl || user.avatar;
      }
    }

    // 生成JWT token
    const token = jwt.sign(
      { userId: user.id, openid: user.openid },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );

    res.json({
      success: true,
      data: {
        token,
        user: {
          id: user.id,
          nickname: user.nickname,
          avatar: user.avatar,
          user_type: user.user_type,
          level: user.level,
          status: user.status
        }
      }
    });

  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({
      success: false,
      message: '登录失败'
    });
  }
});

// 刷新token
router.post('/refresh-token', async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: '缺少token'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // 验证用户是否存在
    const [users] = await db.execute(
      'SELECT id, openid, nickname, avatar, user_type, level, status FROM users WHERE id = ?',
      [decoded.userId]
    );

    if (users.length === 0) {
      return res.status(401).json({
        success: false,
        message: '用户不存在'
      });
    }

    const user = users[0];

    if (user.status === 'banned') {
      return res.status(403).json({
        success: false,
        message: '用户已被禁用'
      });
    }

    // 生成新token
    const newToken = jwt.sign(
      { userId: user.id, openid: user.openid },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );

    res.json({
      success: true,
      data: {
        token: newToken,
        user: {
          id: user.id,
          nickname: user.nickname,
          avatar: user.avatar,
          user_type: user.user_type,
          level: user.level,
          status: user.status
        }
      }
    });

  } catch (error) {
    res.status(401).json({
      success: false,
      message: '无效的token'
    });
  }
});

module.exports = router;
