{"name": "usedcar-auction-backend", "version": "1.0.0", "description": "二手车拍卖平台后端API", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "init-db": "node database/init.js"}, "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.2", "moment": "^2.29.4"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["auction", "car", "miniprogram"], "author": "", "license": "MIT"}