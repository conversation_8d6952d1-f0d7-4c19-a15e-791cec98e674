const jwt = require('jsonwebtoken');
const db = require('../config/database');

// 验证JWT token
const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ 
      success: false, 
      message: '未提供访问令牌' 
    });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // 验证用户是否存在
    const [users] = await db.execute(
      'SELECT id, openid, nickname, user_type, level, status FROM users WHERE id = ?',
      [decoded.userId]
    );

    if (users.length === 0) {
      return res.status(401).json({ 
        success: false, 
        message: '用户不存在' 
      });
    }

    if (users[0].status === 'banned') {
      return res.status(403).json({ 
        success: false, 
        message: '用户已被禁用' 
      });
    }

    req.user = users[0];
    next();
  } catch (error) {
    return res.status(403).json({ 
      success: false, 
      message: '无效的访问令牌' 
    });
  }
};

// 验证用户类型
const requireUserType = (types) => {
  return (req, res, next) => {
    if (!types.includes(req.user.user_type) && req.user.user_type !== 'both') {
      return res.status(403).json({ 
        success: false, 
        message: '权限不足' 
      });
    }
    next();
  };
};

// 验证用户等级
const requireUserLevel = (levels) => {
  return (req, res, next) => {
    if (!levels.includes(req.user.level)) {
      return res.status(403).json({ 
        success: false, 
        message: '用户等级不足' 
      });
    }
    next();
  };
};

module.exports = {
  authenticateToken,
  requireUserType,
  requireUserLevel
};
