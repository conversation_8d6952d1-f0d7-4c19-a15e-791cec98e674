const express = require('express');
const db = require('../config/database');
const { authenticateToken, requireUserType } = require('../middleware/auth');

const router = express.Router();

// 获取拍卖列表
router.get('/', async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      status, 
      brand, 
      model, 
      price_min, 
      price_max,
      keyword 
    } = req.query;

    const offset = (page - 1) * limit;
    let whereConditions = [];
    let params = [];

    // 构建查询条件
    if (status) {
      whereConditions.push('a.status = ?');
      params.push(status);
    }

    if (brand) {
      whereConditions.push('v.brand = ?');
      params.push(brand);
    }

    if (model) {
      whereConditions.push('v.model LIKE ?');
      params.push(`%${model}%`);
    }

    if (price_min) {
      whereConditions.push('a.current_price >= ?');
      params.push(price_min);
    }

    if (price_max) {
      whereConditions.push('a.current_price <= ?');
      params.push(price_max);
    }

    if (keyword) {
      whereConditions.push('(a.title LIKE ? OR v.brand LIKE ? OR v.model LIKE ?)');
      params.push(`%${keyword}%`, `%${keyword}%`, `%${keyword}%`);
    }

    const whereClause = whereConditions.length > 0 
      ? 'WHERE ' + whereConditions.join(' AND ') 
      : '';

    // 查询拍卖列表
    const [auctions] = await db.execute(`
      SELECT 
        a.id,
        a.title,
        a.starting_price,
        a.current_price,
        a.deposit_amount,
        a.start_time,
        a.end_time,
        a.status,
        a.total_bids,
        v.brand,
        v.model,
        v.year,
        v.mileage,
        v.images,
        u.nickname as seller_name
      FROM auctions a
      JOIN vehicles v ON a.vehicle_id = v.id
      JOIN users u ON v.seller_id = u.id
      ${whereClause}
      ORDER BY a.created_at DESC
      LIMIT ? OFFSET ?
    `, [...params, parseInt(limit), offset]);

    // 查询总数
    const [countResult] = await db.execute(`
      SELECT COUNT(*) as total
      FROM auctions a
      JOIN vehicles v ON a.vehicle_id = v.id
      ${whereClause}
    `, params);

    const total = countResult[0].total;

    res.json({
      success: true,
      data: {
        auctions: auctions.map(auction => ({
          ...auction,
          images: JSON.parse(auction.images || '[]')
        })),
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取拍卖列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取拍卖列表失败'
    });
  }
});

// 获取拍卖详情
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // 查询拍卖详情
    const [auctions] = await db.execute(`
      SELECT 
        a.*,
        v.*,
        u.nickname as seller_name,
        u.avatar as seller_avatar
      FROM auctions a
      JOIN vehicles v ON a.vehicle_id = v.id
      JOIN users u ON v.seller_id = u.id
      WHERE a.id = ?
    `, [id]);

    if (auctions.length === 0) {
      return res.status(404).json({
        success: false,
        message: '拍卖不存在'
      });
    }

    const auction = auctions[0];

    // 查询出价记录
    const [bids] = await db.execute(`
      SELECT 
        b.amount,
        b.bid_time,
        u.nickname
      FROM bids b
      JOIN users u ON b.user_id = u.id
      WHERE b.auction_id = ?
      ORDER BY b.bid_time DESC
      LIMIT 20
    `, [id]);

    res.json({
      success: true,
      data: {
        ...auction,
        images: JSON.parse(auction.images || '[]'),
        videos: JSON.parse(auction.videos || '[]'),
        bids
      }
    });

  } catch (error) {
    console.error('获取拍卖详情错误:', error);
    res.status(500).json({
      success: false,
      message: '获取拍卖详情失败'
    });
  }
});

// 创建拍卖
router.post('/', authenticateToken, requireUserType(['seller']), async (req, res) => {
  try {
    const {
      vehicle_id,
      title,
      starting_price,
      reserve_price,
      deposit_amount,
      start_time,
      end_time,
      bid_increment
    } = req.body;

    // 验证车辆是否属于当前用户
    const [vehicles] = await db.execute(
      'SELECT * FROM vehicles WHERE id = ? AND seller_id = ? AND status = "approved"',
      [vehicle_id, req.user.id]
    );

    if (vehicles.length === 0) {
      return res.status(400).json({
        success: false,
        message: '车辆不存在或未通过审核'
      });
    }

    // 创建拍卖
    const [result] = await db.execute(`
      INSERT INTO auctions (
        vehicle_id, title, starting_price, reserve_price, 
        deposit_amount, start_time, end_time, bid_increment
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      vehicle_id, title, starting_price, reserve_price,
      deposit_amount, start_time, end_time, bid_increment || 1000
    ]);

    // 更新车辆状态
    await db.execute(
      'UPDATE vehicles SET status = "published" WHERE id = ?',
      [vehicle_id]
    );

    res.json({
      success: true,
      data: {
        auction_id: result.insertId
      }
    });

  } catch (error) {
    console.error('创建拍卖错误:', error);
    res.status(500).json({
      success: false,
      message: '创建拍卖失败'
    });
  }
});

// 获取我的拍卖（卖家）
router.get('/my/selling', authenticateToken, requireUserType(['seller']), async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;
    const offset = (page - 1) * limit;

    let whereCondition = 'WHERE v.seller_id = ?';
    let params = [req.user.id];

    if (status) {
      whereCondition += ' AND a.status = ?';
      params.push(status);
    }

    const [auctions] = await db.execute(`
      SELECT 
        a.id,
        a.title,
        a.starting_price,
        a.current_price,
        a.start_time,
        a.end_time,
        a.status,
        a.total_bids,
        v.brand,
        v.model,
        v.images
      FROM auctions a
      JOIN vehicles v ON a.vehicle_id = v.id
      ${whereCondition}
      ORDER BY a.created_at DESC
      LIMIT ? OFFSET ?
    `, [...params, parseInt(limit), offset]);

    res.json({
      success: true,
      data: auctions.map(auction => ({
        ...auction,
        images: JSON.parse(auction.images || '[]')
      }))
    });

  } catch (error) {
    console.error('获取我的拍卖错误:', error);
    res.status(500).json({
      success: false,
      message: '获取我的拍卖失败'
    });
  }
});

module.exports = router;
