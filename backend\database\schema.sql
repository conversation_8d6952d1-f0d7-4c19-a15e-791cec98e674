-- 二手车拍卖平台数据库设计

-- 用户表
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    openid VARCHAR(100) UNIQUE NOT NULL COMMENT '微信openid',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar VARCHAR(255) COMMENT '头像',
    phone VARCHAR(20) COMMENT '手机号',
    user_type ENUM('buyer', 'seller', 'both') DEFAULT 'buyer' COMMENT '用户类型',
    level ENUM('normal', 'member', 'vip') DEFAULT 'normal' COMMENT '用户等级',
    status ENUM('active', 'banned') DEFAULT 'active' COMMENT '用户状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 地址表
CREATE TABLE addresses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    name VA<PERSON>HAR(50) NOT NULL COMMENT '收货人姓名',
    phone VARCHAR(20) NOT NULL COMMENT '联系电话',
    province VARCHAR(50) NOT NULL COMMENT '省份',
    city VARCHAR(50) NOT NULL COMMENT '城市',
    district VARCHAR(50) NOT NULL COMMENT '区县',
    detail VARCHAR(255) NOT NULL COMMENT '详细地址',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否默认地址',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 车辆信息表
CREATE TABLE vehicles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    seller_id INT NOT NULL COMMENT '卖家ID',
    brand VARCHAR(50) NOT NULL COMMENT '品牌',
    model VARCHAR(100) NOT NULL COMMENT '车型',
    year INT NOT NULL COMMENT '年份',
    mileage INT NOT NULL COMMENT '里程数(公里)',
    color VARCHAR(30) COMMENT '颜色',
    fuel_type ENUM('gasoline', 'diesel', 'electric', 'hybrid') COMMENT '燃料类型',
    transmission ENUM('manual', 'automatic') COMMENT '变速箱类型',
    engine_size DECIMAL(3,1) COMMENT '排量',
    description TEXT COMMENT '车辆描述',
    condition_report TEXT COMMENT '检测报告',
    images JSON COMMENT '车辆图片',
    videos JSON COMMENT '车辆视频',
    status ENUM('pending', 'approved', 'rejected', 'published', 'sold') DEFAULT 'pending' COMMENT '审核状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (seller_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 拍卖表
CREATE TABLE auctions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    vehicle_id INT NOT NULL,
    title VARCHAR(200) NOT NULL COMMENT '拍卖标题',
    starting_price DECIMAL(10,2) NOT NULL COMMENT '起拍价',
    reserve_price DECIMAL(10,2) COMMENT '保留价',
    deposit_amount DECIMAL(10,2) NOT NULL COMMENT '保证金',
    current_price DECIMAL(10,2) DEFAULT 0 COMMENT '当前价格',
    bid_increment DECIMAL(10,2) DEFAULT 1000 COMMENT '加价幅度',
    start_time TIMESTAMP NOT NULL COMMENT '开始时间',
    end_time TIMESTAMP NOT NULL COMMENT '结束时间',
    status ENUM('upcoming', 'active', 'ended', 'cancelled') DEFAULT 'upcoming' COMMENT '拍卖状态',
    winner_id INT COMMENT '中标者ID',
    total_bids INT DEFAULT 0 COMMENT '总出价次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id) ON DELETE CASCADE,
    FOREIGN KEY (winner_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 出价记录表
CREATE TABLE bids (
    id INT PRIMARY KEY AUTO_INCREMENT,
    auction_id INT NOT NULL,
    user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL COMMENT '出价金额',
    bid_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '出价时间',
    is_auto BOOLEAN DEFAULT FALSE COMMENT '是否自动出价',
    FOREIGN KEY (auction_id) REFERENCES auctions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_auction_amount (auction_id, amount DESC),
    INDEX idx_auction_time (auction_id, bid_time DESC)
);

-- 保证金记录表
CREATE TABLE deposits (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    auction_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL COMMENT '保证金金额',
    status ENUM('paid', 'refunded', 'forfeited') DEFAULT 'paid' COMMENT '保证金状态',
    payment_method VARCHAR(50) COMMENT '支付方式',
    transaction_id VARCHAR(100) COMMENT '交易流水号',
    paid_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '支付时间',
    refunded_at TIMESTAMP NULL COMMENT '退还时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (auction_id) REFERENCES auctions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_auction (user_id, auction_id)
);

-- 订单表
CREATE TABLE orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_no VARCHAR(50) UNIQUE NOT NULL COMMENT '订单号',
    auction_id INT NOT NULL,
    buyer_id INT NOT NULL,
    seller_id INT NOT NULL,
    vehicle_id INT NOT NULL,
    final_price DECIMAL(10,2) NOT NULL COMMENT '成交价格',
    deposit_amount DECIMAL(10,2) NOT NULL COMMENT '保证金',
    remaining_amount DECIMAL(10,2) NOT NULL COMMENT '剩余金额',
    status ENUM('pending_payment', 'paid', 'completed', 'cancelled') DEFAULT 'pending_payment' COMMENT '订单状态',
    payment_deadline TIMESTAMP COMMENT '付款截止时间',
    delivery_address_id INT COMMENT '收货地址ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (auction_id) REFERENCES auctions(id) ON DELETE CASCADE,
    FOREIGN KEY (buyer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (seller_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id) ON DELETE CASCADE,
    FOREIGN KEY (delivery_address_id) REFERENCES addresses(id) ON DELETE SET NULL
);

-- 心愿车源表
CREATE TABLE wishlists (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    brand VARCHAR(50) COMMENT '品牌',
    model VARCHAR(100) COMMENT '车型',
    year_min INT COMMENT '最小年份',
    year_max INT COMMENT '最大年份',
    price_min DECIMAL(10,2) COMMENT '最低价格',
    price_max DECIMAL(10,2) COMMENT '最高价格',
    mileage_max INT COMMENT '最大里程',
    fuel_type ENUM('gasoline', 'diesel', 'electric', 'hybrid') COMMENT '燃料类型',
    transmission ENUM('manual', 'automatic') COMMENT '变速箱类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 轮播广告表
CREATE TABLE banners (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(100) NOT NULL COMMENT '广告标题',
    image VARCHAR(255) NOT NULL COMMENT '广告图片',
    link_type ENUM('auction', 'external', 'none') DEFAULT 'none' COMMENT '链接类型',
    link_value VARCHAR(255) COMMENT '链接值',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    start_time TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP COMMENT '结束时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 系统配置表
CREATE TABLE system_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(255) COMMENT '配置描述',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 插入默认配置
INSERT INTO system_configs (config_key, config_value, description) VALUES
('bid_extend_time', '300', '拍卖延时时间(秒)'),
('max_bid_increment', '50000', '最大加价幅度'),
('min_bid_increment', '100', '最小加价幅度'),
('payment_deadline_hours', '24', '付款截止时间(小时)');
